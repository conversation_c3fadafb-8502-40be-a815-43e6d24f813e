using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using Microsoft.Extensions.Logging;
using Webaby.Security;
using LinqToDB;
using TinyCRM.ServiceType;
using TinyCRM.TaskType;
using TinyCRM.ServiceCategory;
using Webaby.Core.DueTime;
using System.Linq;
using Microsoft.EntityFrameworkCore;

namespace Webaby.Core.DueTime.Queries
{
    /// <summary>
    /// LinqToDB implementation để thay thế stored procedure SearchServiceTypeTaskTypeByDueTimeId
    /// </summary>
    public class GetServiceTypeTaskTypeByDueTimeIdLinqToDBQuery : QueryBase<ServiceTypeTaskTypeByDueTimeIdData>
    {
        public Guid DueTimeId { get; set; }
    }

    internal class GetServiceTypeTaskTypeByDueTimeIdLinqToDBQueryHandler :
        QueryHandlerBase<GetServiceTypeTaskTypeByDueTimeIdLinqToDBQuery, ServiceTypeTaskTypeByDueTimeIdData>
    {
        private readonly ILogger<GetServiceTypeTaskTypeByDueTimeIdLinqToDBQueryHandler> _logger;
        private readonly IUserService _userService;

        public GetServiceTypeTaskTypeByDueTimeIdLinqToDBQueryHandler(
            IServiceProvider serviceProvider,
            IUserService userService,
            ILogger<GetServiceTypeTaskTypeByDueTimeIdLinqToDBQueryHandler> logger)
            : base(serviceProvider)
        {
            _logger = logger;
            _userService = userService;
        }

        /// <summary>
        /// Convert stored procedure SearchServiceTypeTaskTypeByDueTimeId sang LinqToDB
        /// Match chính xác với logic của stored procedure
        /// </summary>
        public override async Task<QueryResult<ServiceTypeTaskTypeByDueTimeIdData>> ExecuteAsync(GetServiceTypeTaskTypeByDueTimeIdLinqToDBQuery query)
        {
            var dataAuthorizedId = _userService.GetDataAuthorizedId();

            // Tính toán phân trang theo stored procedure: startRow = index * size + 1, endRow = index * size + size
            int startRow = query.Pagination.Index * query.Pagination.Size + 1;
            int endRow = query.Pagination.Index * query.Pagination.Size + query.Pagination.Size;

            // Part 1: ServiceType query (Type = 0) - Match chính xác với stored procedure
            var serviceTypeQuery = (
                from st in EntitySet.Get<ServiceTypeEntity>().ToLinqToDB()
                join dt in EntitySet.Get<DueTimeEntity>().ToLinqToDB() on st.ProcessDueTimeId equals dt.Id // INNER JOIN như trong SP
                join sc1 in EntitySet.Get<ServiceCategoryEntity>().ToLinqToDB() on st.Level1Id equals sc1.Id into sc1Group
                from sc1 in sc1Group.DefaultIfEmpty()
                join sc2 in EntitySet.Get<ServiceCategoryEntity>().ToLinqToDB() on st.Level2Id equals sc2.Id into sc2Group
                from sc2 in sc2Group.DefaultIfEmpty()
                join sc3 in EntitySet.Get<ServiceCategoryEntity>().ToLinqToDB() on st.Level3Id equals sc3.Id into sc3Group
                from sc3 in sc3Group.DefaultIfEmpty()
                join sc4 in EntitySet.Get<ServiceCategoryEntity>().ToLinqToDB() on st.Level4Id equals sc4.Id into sc4Group
                from sc4 in sc4Group.DefaultIfEmpty()
                where (dataAuthorizedId == null || st.DataAuthorizedId == dataAuthorizedId) &&
                      (st.ProcessDueTimeId == query.DueTimeId || st.AcceptDueTimeId == query.DueTimeId) &&
                      !st.Deleted
                select new ServiceTypeTaskTypeByDueTimeIdData
                {
                    Id = st.Id,
                    // Build name theo format của stored procedure: IIF(sc1.Name IS NOT NULL,sc1.Name, '') + IIF(sc2.Name IS NOT NULL,'-' + sc2.Name, '') + ...
                    Name = (sc1.Name ?? "") +
                           (sc2.Name != null ? "-" + sc2.Name : "") +
                           (sc3.Name != null ? "-" + sc3.Name : "") +
                           (sc4.Name != null ? "-" + sc4.Name : ""),
                    ProcessDueTimeId = st.ProcessDueTimeId,
                    AcceptDueTimeId = st.AcceptDueTimeId,
                    Type = 0 // ServiceType
                }
            );

            // Part 2: TaskType query (Type = 1) - Match chính xác với stored procedure
            var taskTypeQuery = (
                from tt in EntitySet.Get<TaskTypeEntity>().ToLinqToDB()
                join dt in EntitySet.Get<DueTimeEntity>().ToLinqToDB() on tt.ProcessDueTimeId equals dt.Id // INNER JOIN như trong SP
                where (tt.ProcessDueTimeId == query.DueTimeId || tt.AcceptDueTimeId == query.DueTimeId) &&
                      !tt.Deleted
                select new ServiceTypeTaskTypeByDueTimeIdData
                {
                    Id = tt.Id,
                    Name = tt.TaskType,
                    ProcessDueTimeId = tt.ProcessDueTimeId,
                    AcceptDueTimeId = tt.AcceptDueTimeId,
                    Type = 1 // TaskType
                }
            );

            // Union hai queries và implement CTE với ROW_NUMBER() như trong stored procedure
            var unionQuery = serviceTypeQuery.Union(taskTypeQuery);

            // Implement CTE với ROW_NUMBER() window function như trong stored procedure
            var resultWithRowNumber = (
                from item in unionQuery
                let rowNumber = Sql.Ext.RowNumber().Over().OrderBy(1).ToValue() // ROW_NUMBER() OVER (ORDER BY (SELECT 1))
                select new
                {
                    Data = item,
                    RowNumber = rowNumber
                }
            );

            // Đếm tổng số records (SELECT COUNT(*) FROM result)
            var totalCount = await resultWithRowNumber.CountAsync();

            // Áp dụng phân trang với BETWEEN như trong stored procedure
            var pagedResult = await (
                from item in resultWithRowNumber
                where item.RowNumber >= startRow && item.RowNumber <= endRow // WHERE RowNumber BETWEEN @startRow AND @endRow
                select new ServiceTypeTaskTypeByDueTimeIdData
                {
                    Id = item.Data.Id,
                    Name = item.Data.Name,
                    ProcessDueTimeId = item.Data.ProcessDueTimeId,
                    AcceptDueTimeId = item.Data.AcceptDueTimeId,
                    Type = item.Data.Type,
                    TotalCount = totalCount // (SELECT COUNT(*) FROM result) TotalCount
                }
            ).ToListAsync();

            _logger.LogInformation("GetServiceTypeTaskTypeByDueTimeIdLinqToDB executed: DueTimeId={DueTimeId}, TotalCount={TotalCount}, PageSize={PageSize}",
                query.DueTimeId, totalCount, pagedResult.Count);

            return new QueryResult<ServiceTypeTaskTypeByDueTimeIdData>(pagedResult);
        }
    }
}
