using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using Microsoft.Extensions.Logging;
using Webaby.Security;
using LinqToDB;
using TinyCRM.ServiceType;
using TinyCRM.TaskType;
using TinyCRM.ServiceCategory;
using System.Linq;
using Microsoft.EntityFrameworkCore;

namespace Webaby.Core.DueTime.Queries
{
    /// <summary>
    /// LinqToDB implementation để thay thế stored procedure SearchServiceTypeTaskTypeByDueTimeId
    /// </summary>
    public class GetServiceTypeTaskTypeByDueTimeIdLinqToDBQuery : QueryBase<ServiceTypeTaskTypeByDueTimeIdData>
    {
        public Guid DueTimeId { get; set; }
    }

    internal class GetServiceTypeTaskTypeByDueTimeIdLinqToDBQueryHandler :
        QueryHandlerBase<GetServiceTypeTaskTypeByDueTimeIdLinqToDBQuery, ServiceTypeTaskTypeByDueTimeIdData>
    {
        private readonly ILogger<GetServiceTypeTaskTypeByDueTimeIdLinqToDBQueryHandler> _logger;
        private readonly IUserService _userService;

        public GetServiceTypeTaskTypeByDueTimeIdLinqToDBQueryHandler(
            IServiceProvider serviceProvider,
            IUserService userService,
            ILogger<GetServiceTypeTaskTypeByDueTimeIdLinqToDBQueryHandler> logger)
            : base(serviceProvider)
        {
            _logger = logger;
            _userService = userService;
        }

        /// <summary>
        /// Convert stored procedure SearchServiceTypeTaskTypeByDueTimeId sang LinqToDB
        /// </summary>
        public override async Task<QueryResult<ServiceTypeTaskTypeByDueTimeIdData>> ExecuteAsync(GetServiceTypeTaskTypeByDueTimeIdLinqToDBQuery query)
        {
            var dataAuthorizedId = _userService.GetDataAuthorizedId();

            // Tính toán phân trang
            int skip = query.Pagination.Index * query.Pagination.Size;
            int take = query.Pagination.Size;

            // Part 1: ServiceType query (Type = 0)
            var serviceTypeQuery = (
                from st in EntitySet.Get<ServiceTypeEntity>().ToLinqToDB()
                join sc1 in EntitySet.Get<ServiceCategoryEntity>().ToLinqToDB() on st.Level1Id equals sc1.Id into sc1Group
                from sc1 in sc1Group.DefaultIfEmpty()
                join sc2 in EntitySet.Get<ServiceCategoryEntity>().ToLinqToDB() on st.Level2Id equals sc2.Id into sc2Group
                from sc2 in sc2Group.DefaultIfEmpty()
                join sc3 in EntitySet.Get<ServiceCategoryEntity>().ToLinqToDB() on st.Level3Id equals sc3.Id into sc3Group
                from sc3 in sc3Group.DefaultIfEmpty()
                join sc4 in EntitySet.Get<ServiceCategoryEntity>().ToLinqToDB() on st.Level4Id equals sc4.Id into sc4Group
                from sc4 in sc4Group.DefaultIfEmpty()
                where (dataAuthorizedId == null || st.DataAuthorizedId == dataAuthorizedId) &&
                      (st.ProcessDueTimeId == query.DueTimeId || st.AcceptDueTimeId == query.DueTimeId) &&
                      !st.Deleted
                select new ServiceTypeTaskTypeByDueTimeIdData
                {
                    Id = st.Id,
                    // Build name theo format của stored procedure: sc1 + "-" + sc2 + "-" + sc3 + "-" + sc4
                    Name = (sc1.Name ?? "") +
                           (sc2.Name != null ? "-" + sc2.Name : "") +
                           (sc3.Name != null ? "-" + sc3.Name : "") +
                           (sc4.Name != null ? "-" + sc4.Name : ""),
                    ProcessDueTimeId = st.ProcessDueTimeId,
                    AcceptDueTimeId = st.AcceptDueTimeId,
                    Type = 0 // ServiceType
                }
            );

            // Part 2: TaskType query (Type = 1)
            var taskTypeQuery = (
                from tt in EntitySet.Get<TaskTypeEntity>().ToLinqToDB()
                where (tt.ProcessDueTimeId == query.DueTimeId || tt.AcceptDueTimeId == query.DueTimeId) &&
                      !tt.Deleted
                select new ServiceTypeTaskTypeByDueTimeIdData
                {
                    Id = tt.Id,
                    Name = tt.TaskType,
                    ProcessDueTimeId = tt.ProcessDueTimeId,
                    AcceptDueTimeId = tt.AcceptDueTimeId,
                    Type = 1 // TaskType
                }
            );

            // Union hai queries
            var unionQuery = serviceTypeQuery.Union(taskTypeQuery);

            // Đếm tổng số records
            var totalCount = await unionQuery.CountAsync();

            // Áp dụng phân trang đơn giản với Skip/Take
            var pagedResult = await unionQuery
                .Skip(skip)
                .Take(take)
                .ToListAsync();

            // Set TotalCount cho tất cả items
            foreach (var item in pagedResult)
            {
                item.TotalCount = totalCount;
            }

            _logger.LogInformation("GetServiceTypeTaskTypeByDueTimeIdLinqToDB executed: DueTimeId={DueTimeId}, TotalCount={TotalCount}, PageSize={PageSize}",
                query.DueTimeId, totalCount, pagedResult.Count);

            return new QueryResult<ServiceTypeTaskTypeByDueTimeIdData>(pagedResult);
        }
    }
}
