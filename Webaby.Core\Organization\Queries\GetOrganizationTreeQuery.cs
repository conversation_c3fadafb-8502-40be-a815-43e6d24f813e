﻿using LinqToDB;
using LinqToDB.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using static Webaby.Core.Organization.Queries.GetOrganizationTreeQuery;

namespace Webaby.Core.Organization.Queries
{
    public class GetOrganizationTreeQuery : QueryBase<OrganizationData>
    {
        public string OrganizationType
        {
            get;
            set;
        }

        public Guid? ProvinceId
        {
            get;
            set;
        }

        public class OrgTreeItem
        {
            public Guid OrgId { get; set; }
            public string Name { get; set; }
            public string OrganizationType { get; set; }
            public Guid? ParentId { get; set; }
            public string OrderInGroup { get; set; }
            public int NodeLevel { get; set; }
        }
    }

    internal class GetOrganizationTreeQueryHandler(IServiceProvider serviceProvider, DatabaseContext baseContext) : QueryHandlerBase<GetOrganizationTreeQuery, OrganizationData>(serviceProvider)
    {
        private readonly DatabaseContext _dbContext = baseContext;

        public override async Task<QueryResult<OrganizationData>> ExecuteAsync(GetOrganizationTreeQuery query)
        {
            // Step 1: Tạo LinqToDB context từ EF DbContext
            var linq2dbCtx = _dbContext.CreateLinqToDBContext();

            // Step 2: Truy cập bảng
            var orgTable = EntitySet.Get<OrganizationEntity>();
            var workAreaTable = EntitySet.Get<OrganizationWorkingAreaEntity>();

            // Step 3: Gọi CTE đệ quy
            var allOrgsCte = linq2dbCtx.GetCte<OrgTreeItem>(cte =>
            {
                var baseQuery = orgTable
                    .Where(o => o.ParentId == null)
                    .Select(o => new OrgTreeItem
                    {
                        OrgId = o.Id,
                        Name = o.Name,
                        OrganizationType = o.OrganizationType,
                        ParentId = o.ParentId,
                        OrderInGroup = Sql.Convert<string, long>(Sql.Types.VarChar(50), 0),
                        NodeLevel = 0
                    });
                //Đệ quy
                var recursiveQuery =
                    from c in orgTable
                    join p in cte on c.ParentId equals p.OrgId
                    let rowNumber = Sql.Ext.RowNumber().Over().OrderBy(c.Name).ToValue()
                    select new OrgTreeItem
                    {
                        OrgId = c.Id,
                        Name = c.Name,
                        OrganizationType = c.OrganizationType,
                        ParentId = c.ParentId,
                        OrderInGroup = Sql.Convert<string, string>(Sql.Types.VarChar(50), p.OrderInGroup + rowNumber.ToString()),
                        NodeLevel = p.NodeLevel + 1
                    };
                
                return baseQuery.Concat(recursiveQuery);
            });

            //Giả lập bảng tạm chứa kết quả của CTE đầu tiên
            var rawTemp = allOrgsCte
                .Select(o => new OrgTreeItem
                {
                    OrgId = o.OrgId,
                    Name = o.Name,
                    OrganizationType = o.OrganizationType,
                    ParentId = o.ParentId,
                    OrderInGroup = o.OrderInGroup,
                    NodeLevel = o.NodeLevel
                });

            // 3) CTE thứ hai: đệ quy lên trên trong bảng tạm
            var filteredCte = linq2dbCtx.GetCte<OrgTreeItem>(cte2 =>
            {
                // Base: filer data từ bảng tạm
                var base2 = from t in rawTemp
                            join w in workAreaTable on t.OrgId equals w.OrganizationId into wg
                            from work in wg.DefaultIfEmpty()
                            where (string.IsNullOrEmpty(query.OrganizationType) || t.OrganizationType == query.OrganizationType)
                                  && (query.ProvinceId == null || work.ProvinceId == query.ProvinceId)
                            select t;

                // Đệ quy
                var rec2 = from t in rawTemp
                           join p in cte2 on t.OrgId equals p.ParentId
                           select t;

                return base2.Concat(rec2);
            });

            // 4) Lấy kết quả cuối cùng, distinct và order by OrderInGroup
            var result = (await filteredCte
                .Distinct()
                .OrderBy(r => r.OrderInGroup)
                .Select(x => new OrganizationData
                {
                    Id = x.OrgId,
                    Name = x.Name,
                    OrganizationType = x.OrganizationType,
                    ParentId = x.ParentId,
                    NodeLevel = x.NodeLevel
                }).ToListAsync());

            return QueryResult.Create(result);

            //var cmd = EntitySet.CreateDbCommand();
            //cmd.CommandText = "dbo.GetOrganizationTree";
            //cmd.CommandType = CommandType.StoredProcedure;
            //cmd.Parameters.AddRange(new[]
            //{
            //    DbParameterHelper.AddNullableString(cmd, "@OrganizationType", query.OrganizationType),
            //    DbParameterHelper.AddNullableGuid(cmd, "@ProvinceId", query.ProvinceId)
            //});

            //var mainQuery = await EntitySet.ExecuteReadCommandAsync<OrganizationData>(cmd);
            //return new QueryResult<OrganizationData>(mainQuery);
        }
    }
}
