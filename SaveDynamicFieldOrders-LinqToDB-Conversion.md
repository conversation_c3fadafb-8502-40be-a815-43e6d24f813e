# Convert SaveDynamicFieldOrders Stored Procedure sang LinqToDB

## Tổng quan

Đây là ví dụ convert stored procedure `dbo.SaveDynamicFieldOrders` sang LinqToDB, xử lý table parameter và đảm bảo hoạt động hiệu quả trên cả SQL Server và PostgreSQL.

## Thách thức chính

### 1. **Table Parameter (SQL Server only)**
```sql
-- SQL Server Table Type
CREATE TYPE [dbo].[SortIdListSectionId] AS TABLE(
    [Id] [uniqueidentifier] NULL,
    [SortOrder] [int] NULL,
    [DynamicFieldSectionId] [uniqueidentifier] NULL
)

-- Stored Procedure sử dụng Table Parameter
CREATE PROCEDURE [dbo].[SaveDynamicFieldOrders]
    @DynamicFormId UNIQUEIDENTIFIER,
    @UserId UNIQUEIDENTIFIER,
    @DynamicFieldOrders dbo.SortIdListSectionId READONLY
```

### 2. **PostgreSQL không hỗ trợ Table Parameter**
PostgreSQL không có khái niệm table parameter như SQL Server.

## Stored Procedure gốc

```sql
CREATE PROCEDURE [dbo].[SaveDynamicFieldOrders]
    @DynamicFormId UNIQUEIDENTIFIER,
    @UserId UNIQUEIDENTIFIER,
    @DynamicFieldOrders dbo.SortIdListSectionId READONLY
AS
BEGIN
    -- Step 1: Update basic fields
    UPDATE df
    SET [Order] = dfo.SortOrder, 
        ModifiedBy = @UserId, 
        ModifiedDate = GETDATE(), 
        DynamicFieldSectionId = dfo.DynamicFieldSectionId
    FROM dbo.DynamicFieldDefinition df
    JOIN @DynamicFieldOrders dfo ON dfo.Id = df.Id
    WHERE df.DynamicFormId = @DynamicFormId

    -- Step 2: Correct Order cho RepresentationDynamicField (complex logic)
    UPDATE dfd
    SET dfd.[Order] = tempNewOrder.NewOrder
    FROM dbo.DynamicFieldDefinition dfd
    JOIN (
        SELECT df.Id, ROW_NUMBER() OVER (ORDER BY ISNULL(temp.DynamicFieldOrder, df.[Order])) NewOrder
        FROM dbo.DynamicFieldDefinition df
        LEFT JOIN (
            SELECT dfd.Id, temp.RepresentationDynamicFieldOrder + CAST(dfd.[Order] AS FLOAT)/1000 AS DynamicFieldOrder
            FROM dbo.DynamicFieldDefinition dfd
            JOIN (
                SELECT dfd.RepresentationDynamicFieldId, MIN(dfd.[Order]) RepresentationDynamicFieldOrder
                FROM dbo.DynamicFieldDefinition dfd
                WHERE dfd.DynamicFormId = @DynamicFormId
                    AND dfd.RepresentationDynamicFieldId IS NOT NULL
                GROUP BY dfd.RepresentationDynamicFieldId
            ) temp ON temp.RepresentationDynamicFieldId = dfd.RepresentationDynamicFieldId
        ) temp ON temp.Id = df.Id
        WHERE df.DynamicFormId = @DynamicFormId
    ) tempNewOrder ON tempNewOrder.Id = dfd.Id

    -- Step 3: Update DynamicFieldSectionId cho RepresentationDynamicField
    UPDATE dfd
    SET dfd.DynamicFieldSectionId = temp.DynamicFieldSectionId
    FROM dbo.DynamicFieldDefinition dfd
    JOIN (
        SELECT *
        FROM (
            SELECT dfd.Id, dfd.RepresentationDynamicFieldId, dfd.DynamicFieldSectionId, 
                   ROW_NUMBER() OVER (PARTITION BY dfd.RepresentationDynamicFieldId ORDER BY dfd.[Order]) RowNumber
            FROM dbo.DynamicFieldDefinition dfd
            WHERE dfd.DynamicFormId = @DynamicFormId
                AND dfd.RepresentationDynamicFieldId IS NOT NULL
        ) temp
        WHERE temp.RowNumber = 1
    ) temp ON temp.RepresentationDynamicFieldId = dfd.RepresentationDynamicFieldId
END
```

## Implementation cũ (Table Parameter)

```csharp
public override async Task ExecuteAsync(SaveDynamicFieldOrdersCommand command)
{
    var cmd = EntitySet.CreateDbCommand();
    cmd.CommandText = "dbo.SaveDynamicFieldOrders";
    cmd.CommandType = CommandType.StoredProcedure;

    cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@DynamicFormId", command.DynamicFormId));
    cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@UserId", _userService.GetCurrentUser().Id));

    // Tạo Table Parameter (chỉ hoạt động với SQL Server)
    var tvp = new DataTable();
    tvp.Columns.Add("Id", typeof(Guid));
    tvp.Columns.Add("SortOrder", typeof(int));
    tvp.Columns.Add("DynamicFieldSectionId", typeof(Guid));

    foreach (var item in command.DynamicFieldOrders)
    {
        // Validation logic...
        tvp.Rows.Add(item.DynamicFieldId, item.Order, item.DynamicFieldSectionId);
    }

    var sqlParameterTvp = new SqlParameter("@DynamicFieldOrders", SqlDbType.Structured)
    {
        TypeName = "dbo.SortIdListSectionId",
        Value = tvp
    };
    cmd.Parameters.Add(sqlParameterTvp);

    await EntitySet.ExecuteNonQueryAsync(cmd);
}
```

## Implementation mới (LinqToDB - Cross Database)

```csharp
public override async Task ExecuteAsync(SaveDynamicFieldOrdersCommand command)
{
    var userId = _userService.GetCurrentUser().Id;
    var currentDateTime = DateTime.UtcNow;

    // Validation logic giống stored procedure
    await ValidateCalculatedFieldsAsync(command.DynamicFieldOrders);

    // Sử dụng transaction để đảm bảo consistency
    using var transaction = await _dbContext.Database.BeginTransactionAsync();
    try
    {
        var linq2dbCtx = _dbContext.CreateLinqToDBContext();

        // Step 1: Update Order, ModifiedBy, ModifiedDate, DynamicFieldSectionId
        await UpdateFieldOrdersAsync(linq2dbCtx, command.DynamicFormId, command.DynamicFieldOrders, userId, currentDateTime);

        // Step 2: Correct Order cho RepresentationDynamicField
        await CorrectRepresentationFieldOrdersAsync(linq2dbCtx, command.DynamicFormId);

        // Step 3: Update DynamicFieldSectionId cho RepresentationDynamicField
        await UpdateRepresentationFieldSectionsAsync(linq2dbCtx, command.DynamicFormId);

        await transaction.CommitAsync();
    }
    catch (Exception ex)
    {
        await transaction.RollbackAsync();
        throw;
    }
}
```

## Key Implementation Details

### 1. **Step 1: Basic Field Updates**

```csharp
private async Task UpdateFieldOrdersAsync(LinqToDB.IDataContext linq2dbCtx, Guid dynamicFormId, List<DynamicFieldOrder> fieldOrders, Guid userId, DateTime currentDateTime)
{
    // Thay vì dùng table parameter, update từng field
    foreach (var fieldOrder in fieldOrders)
    {
        await linq2dbCtx.GetTable<DynamicFieldDefinitionEntity>()
            .Where(df => df.Id == fieldOrder.DynamicFieldId && df.DynamicFormId == dynamicFormId)
            .UpdateAsync(df => new DynamicFieldDefinitionEntity
            {
                Order = fieldOrder.Order,
                ModifiedBy = userId,
                ModifiedDate = currentDateTime,
                DynamicFieldSectionId = fieldOrder.DynamicFieldSectionId
            });
    }
}
```

### 2. **Step 2: Complex RepresentationDynamicField Logic**

```csharp
private async Task CorrectRepresentationFieldOrdersAsync(LinqToDB.IDataContext linq2dbCtx, Guid dynamicFormId)
{
    // Tính RepresentationDynamicFieldOrder
    var representationOrders = await (
        from dfd in linq2dbCtx.GetTable<DynamicFieldDefinitionEntity>()
        where dfd.DynamicFormId == dynamicFormId && 
              dfd.RepresentationDynamicFieldId != null
        group dfd by dfd.RepresentationDynamicFieldId into g
        select new
        {
            RepresentationDynamicFieldId = g.Key,
            RepresentationDynamicFieldOrder = g.Min(x => x.Order)
        }
    ).ToListAsync();

    if (representationOrders.Any())
    {
        // Calculate DynamicFieldOrder: RepresentationOrder + Order/1000
        var dynamicFieldOrders = await (
            from dfd in linq2dbCtx.GetTable<DynamicFieldDefinitionEntity>()
            where dfd.DynamicFormId == dynamicFormId
            select new
            {
                dfd.Id,
                dfd.Order,
                dfd.RepresentationDynamicFieldId,
                RepresentationOrder = representationOrders
                    .Where(ro => ro.RepresentationDynamicFieldId == dfd.RepresentationDynamicFieldId)
                    .Select(ro => ro.RepresentationDynamicFieldOrder)
                    .FirstOrDefault()
            }
        ).ToListAsync();

        // ROW_NUMBER() logic trong C#
        var newOrders = dynamicFieldOrders
            .Select(df => new
            {
                df.Id,
                DynamicFieldOrder = df.RepresentationDynamicFieldId != null 
                    ? df.RepresentationOrder + (double)df.Order / 1000 
                    : df.Order,
                OriginalOrder = df.Order
            })
            .OrderBy(x => x.DynamicFieldOrder ?? x.OriginalOrder)
            .Select((x, index) => new { x.Id, NewOrder = index + 1 })
            .ToList();

        // Update new orders
        foreach (var newOrder in newOrders)
        {
            await linq2dbCtx.GetTable<DynamicFieldDefinitionEntity>()
                .Where(dfd => dfd.Id == newOrder.Id)
                .UpdateAsync(dfd => new DynamicFieldDefinitionEntity
                {
                    Order = newOrder.NewOrder
                });
        }
    }
}
```

### 3. **Step 3: Window Function với LinqToDB**

```csharp
private async Task UpdateRepresentationFieldSectionsAsync(LinqToDB.IDataContext linq2dbCtx, Guid dynamicFormId)
{
    // Sử dụng ROW_NUMBER() window function
    var sectionUpdates = await (
        from dfd in linq2dbCtx.GetTable<DynamicFieldDefinitionEntity>()
        where dfd.DynamicFormId == dynamicFormId && 
              dfd.RepresentationDynamicFieldId != null
        let rowNumber = Sql.RowNumber.Over()
            .PartitionBy(dfd.RepresentationDynamicFieldId)
            .OrderBy(dfd.Order)
            .ToValue()
        where rowNumber == 1
        select new
        {
            dfd.RepresentationDynamicFieldId,
            dfd.DynamicFieldSectionId
        }
    ).ToListAsync();

    // Update sections
    foreach (var update in sectionUpdates)
    {
        if (update.RepresentationDynamicFieldId.HasValue)
        {
            await linq2dbCtx.GetTable<DynamicFieldDefinitionEntity>()
                .Where(dfd => dfd.RepresentationDynamicFieldId == update.RepresentationDynamicFieldId)
                .UpdateAsync(dfd => new DynamicFieldDefinitionEntity
                {
                    DynamicFieldSectionId = update.DynamicFieldSectionId
                });
        }
    }
}
```

## Ưu điểm của LinqToDB approach

### ✅ **Cross-Database Compatibility**
- **SQL Server**: Hoạt động bình thường
- **PostgreSQL**: Hoạt động bình thường (không cần table parameter)
- **Khác**: Có thể mở rộng cho MySQL, SQLite, etc.

### 🚀 **Performance & Maintainability**
- **Type Safety**: Compile-time checking
- **Readable**: Logic rõ ràng, dễ hiểu
- **Testable**: Có thể unit test từng step
- **Debuggable**: Có thể debug từng query

### 🔧 **Flexibility**
- **Transaction Support**: Đảm bảo ACID
- **Error Handling**: Proper exception handling
- **Logging**: Chi tiết logging cho monitoring

## Performance Considerations

### 1. **Batch Updates vs Individual Updates**

```csharp
// Current approach: Individual updates (safer, more compatible)
foreach (var fieldOrder in fieldOrders)
{
    await linq2dbCtx.GetTable<DynamicFieldDefinitionEntity>()
        .Where(df => df.Id == fieldOrder.DynamicFieldId)
        .UpdateAsync(df => new DynamicFieldDefinitionEntity { ... });
}

// Alternative: Bulk update (faster, but more complex)
// Có thể implement sau nếu cần optimize performance
```

### 2. **Memory Usage**
- LinqToDB approach sử dụng ít memory hơn table parameter
- Không cần tạo DataTable trong memory

## Testing Strategy

### Unit Test

```csharp
[Test]
public async Task SaveDynamicFieldOrders_LinqToDB_ShouldUpdateCorrectly()
{
    // Arrange
    var formId = Guid.NewGuid();
    await SeedTestDataAsync(formId);
    
    var command = new SaveDynamicFieldOrdersCommand
    {
        DynamicFormId = formId,
        DynamicFieldOrders = new List<DynamicFieldOrder>
        {
            new() { DynamicFieldId = field1Id, Order = 2, DynamicFieldSectionId = section1Id },
            new() { DynamicFieldId = field2Id, Order = 1, DynamicFieldSectionId = section2Id }
        }
    };

    // Act
    await _handler.ExecuteAsync(command);

    // Assert
    var updatedFields = await EntitySet.Get<DynamicFieldDefinitionEntity>()
        .Where(f => f.DynamicFormId == formId)
        .OrderBy(f => f.Order)
        .ToListAsync();
        
    Assert.AreEqual(1, updatedFields[0].Order);
    Assert.AreEqual(2, updatedFields[1].Order);
}
```

## Migration Strategy

### Phase 1: Parallel Implementation
- Deploy LinqToDB version alongside stored procedure
- Feature flag để switch giữa hai approaches

### Phase 2: A/B Testing
- Test performance và correctness
- Monitor error rates và response times

### Phase 3: Complete Migration
- Remove stored procedure dependency
- Clean up table parameter code

## Kết luận

LinqToDB approach giải quyết được vấn đề table parameter và cross-database compatibility, đồng thời cung cấp:

1. **Better maintainability** với type-safe code
2. **Cross-database support** cho SQL Server và PostgreSQL
3. **Proper transaction handling** và error management
4. **Detailed logging** cho monitoring và debugging

Approach này có thể áp dụng cho các stored procedures khác sử dụng table parameters trong codebase TinyCRM.
