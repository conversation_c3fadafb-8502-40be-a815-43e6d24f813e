{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{3C6160E5-0408-43CD-BAB0-E82AE6F10C67}|TinyCRM.Web\\TinyCRM.Web.csproj|c:\\users\\<USER>\\source\\workspaces\\cep_netcore\\tinycrm.web\\views\\duetime\\createedit.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{3C6160E5-0408-43CD-BAB0-E82AE6F10C67}|TinyCRM.Web\\TinyCRM.Web.csproj|solutionrelative:tinycrm.web\\views\\duetime\\createedit.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{3C6160E5-0408-43CD-BAB0-E82AE6F10C67}|TinyCRM.Web\\TinyCRM.Web.csproj|c:\\users\\<USER>\\source\\workspaces\\cep_netcore\\tinycrm.web\\controllers\\duetimecontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3C6160E5-0408-43CD-BAB0-E82AE6F10C67}|TinyCRM.Web\\TinyCRM.Web.csproj|solutionrelative:tinycrm.web\\controllers\\duetimecontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5CD34F6B-E86B-4C98-BC9A-4CABDADDA9C4}|Webaby.Core\\Webaby.Core.csproj|c:\\users\\<USER>\\source\\workspaces\\cep_netcore\\webaby.core\\webaby.core.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{5CD34F6B-E86B-4C98-BC9A-4CABDADDA9C4}|Webaby.Core\\Webaby.Core.csproj|solutionrelative:webaby.core\\webaby.core.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{2FF02035-9624-4B9A-8C3E-19CCE8FA2219}|TinyCRM\\TinyCRM.csproj|c:\\users\\<USER>\\source\\workspaces\\cep_netcore\\tinycrm\\servicetype\\queries\\getservicetypetasktypebyduetimeidquery.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2FF02035-9624-4B9A-8C3E-19CCE8FA2219}|TinyCRM\\TinyCRM.csproj|solutionrelative:tinycrm\\servicetype\\queries\\getservicetypetasktypebyduetimeidquery.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2FF02035-9624-4B9A-8C3E-19CCE8FA2219}|TinyCRM\\TinyCRM.csproj|c:\\users\\<USER>\\source\\workspaces\\cep_netcore\\tinycrm\\requestticket\\commands\\recalculatedynamicformvaluelistcommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2FF02035-9624-4B9A-8C3E-19CCE8FA2219}|TinyCRM\\TinyCRM.csproj|solutionrelative:tinycrm\\requestticket\\commands\\recalculatedynamicformvaluelistcommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2FF02035-9624-4B9A-8C3E-19CCE8FA2219}|TinyCRM\\TinyCRM.csproj|c:\\users\\<USER>\\source\\workspaces\\cep_netcore\\tinycrm\\servicetype\\queries\\getservicetypetreequery.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2FF02035-9624-4B9A-8C3E-19CCE8FA2219}|TinyCRM\\TinyCRM.csproj|solutionrelative:tinycrm\\servicetype\\queries\\getservicetypetreequery.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{3C6160E5-0408-43CD-BAB0-E82AE6F10C67}|TinyCRM.Web\\TinyCRM.Web.csproj|c:\\users\\<USER>\\source\\workspaces\\cep_netcore\\tinycrm.web\\controllers\\dynamictablecontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3C6160E5-0408-43CD-BAB0-E82AE6F10C67}|TinyCRM.Web\\TinyCRM.Web.csproj|solutionrelative:tinycrm.web\\controllers\\dynamictablecontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{3C6160E5-0408-43CD-BAB0-E82AE6F10C67}|TinyCRM.Web\\TinyCRM.Web.csproj|c:\\users\\<USER>\\source\\workspaces\\cep_netcore\\tinycrm.web\\controllers\\dynamicformcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3C6160E5-0408-43CD-BAB0-E82AE6F10C67}|TinyCRM.Web\\TinyCRM.Web.csproj|solutionrelative:tinycrm.web\\controllers\\dynamicformcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 257, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 23, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{809f6ff3-8092-454a-8003-6d4091f9b5bb}"}, {"$type": "Bookmark", "Name": "ST:5:0:{809f6ff3-8092-454a-8003-6d4091f9b5bb}"}, {"$type": "Bookmark", "Name": "ST:3:0:{809f6ff3-8092-454a-8003-6d4091f9b5bb}"}, {"$type": "Bookmark", "Name": "ST:6:0:{809f6ff3-8092-454a-8003-6d4091f9b5bb}"}, {"$type": "Bookmark", "Name": "ST:4:0:{809f6ff3-8092-454a-8003-6d4091f9b5bb}"}, {"$type": "Bookmark", "Name": "ST:2:0:{80454082-9ab8-47d4-af23-82bf6739e2a9}"}, {"$type": "Bookmark", "Name": "ST:18:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:20:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:21:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:19:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:22:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:1:0:{80454082-9ab8-47d4-af23-82bf6739e2a9}"}, {"$type": "Bookmark", "Name": "ST:0:0:{80454082-9ab8-47d4-af23-82bf6739e2a9}"}, {"$type": "Bookmark", "Name": "ST:23:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:24:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:25:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:16:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:17:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:13:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:26:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:14:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:15:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:0:0:{65ddf8c3-8f89-4077-a6c6-dbb8853aab13}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "CreateEdit.cshtml", "DocumentMoniker": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\TinyCRM.Web\\Views\\DueTime\\CreateEdit.cshtml", "RelativeDocumentMoniker": "TinyCRM.Web\\Views\\DueTime\\CreateEdit.cshtml", "ToolTip": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\TinyCRM.Web\\Views\\DueTime\\CreateEdit.cshtml", "RelativeToolTip": "TinyCRM.Web\\Views\\DueTime\\CreateEdit.cshtml", "ViewState": "AgIAAIMBAAAAAAAAAAASwJ4BAAAjAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-07-22T22:58:30.076Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "Webaby.Core", "DocumentMoniker": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\Webaby.Core\\Webaby.Core.csproj", "RelativeDocumentMoniker": "Webaby.Core\\Webaby.Core.csproj", "ToolTip": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\Webaby.Core\\Webaby.Core.csproj", "RelativeToolTip": "Webaby.Core\\Webaby.Core.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-07-22T22:56:55.018Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "DueTimeController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\TinyCRM.Web\\Controllers\\DueTimeController.cs", "RelativeDocumentMoniker": "TinyCRM.Web\\Controllers\\DueTimeController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\TinyCRM.Web\\Controllers\\DueTimeController.cs", "RelativeToolTip": "TinyCRM.Web\\Controllers\\DueTimeController.cs", "ViewState": "AgIAALwAAAAAAAAAAAAgwMgAAABNAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-22T22:55:11.123Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "GetServiceTypeTreeQuery.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\TinyCRM\\ServiceType\\Queries\\GetServiceTypeTreeQuery.cs", "RelativeDocumentMoniker": "TinyCRM\\ServiceType\\Queries\\GetServiceTypeTreeQuery.cs", "ToolTip": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\TinyCRM\\ServiceType\\Queries\\GetServiceTypeTreeQuery.cs", "RelativeToolTip": "TinyCRM\\ServiceType\\Queries\\GetServiceTypeTreeQuery.cs", "ViewState": "AgIAADIAAAAAAAAAAAAIwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-22T09:12:30.706Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "ReCalculateDynamicFormValueListCommand.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\TinyCRM\\RequestTicket\\Commands\\ReCalculateDynamicFormValueListCommand.cs", "RelativeDocumentMoniker": "TinyCRM\\RequestTicket\\Commands\\ReCalculateDynamicFormValueListCommand.cs", "ToolTip": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\TinyCRM\\RequestTicket\\Commands\\ReCalculateDynamicFormValueListCommand.cs", "RelativeToolTip": "TinyCRM\\RequestTicket\\Commands\\ReCalculateDynamicFormValueListCommand.cs", "ViewState": "AgIAAAkAAAAAAAAAAAAuwBUAAABPAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-22T02:06:10.46Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "GetServiceTypeTaskTypeByDueTimeIdQuery.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\TinyCRM\\ServiceType\\Queries\\GetServiceTypeTaskTypeByDueTimeIdQuery.cs", "RelativeDocumentMoniker": "TinyCRM\\ServiceType\\Queries\\GetServiceTypeTaskTypeByDueTimeIdQuery.cs", "ToolTip": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\TinyCRM\\ServiceType\\Queries\\GetServiceTypeTaskTypeByDueTimeIdQuery.cs", "RelativeToolTip": "TinyCRM\\ServiceType\\Queries\\GetServiceTypeTaskTypeByDueTimeIdQuery.cs", "ViewState": "AgIAACIAAAAAAAAAAAAqwHgAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-22T01:47:58.562Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "DynamicTableController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\TinyCRM.Web\\Controllers\\DynamicTableController.cs", "RelativeDocumentMoniker": "TinyCRM.Web\\Controllers\\DynamicTableController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\TinyCRM.Web\\Controllers\\DynamicTableController.cs", "RelativeToolTip": "TinyCRM.Web\\Controllers\\DynamicTableController.cs", "ViewState": "AgIAAH8AAAAAAAAAAAAYwKkAAAAsAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T08:17:01.751Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "DynamicFormController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\TinyCRM.Web\\Controllers\\DynamicFormController.cs", "RelativeDocumentMoniker": "TinyCRM.Web\\Controllers\\DynamicFormController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\TinyCRM.Web\\Controllers\\DynamicFormController.cs", "RelativeToolTip": "TinyCRM.Web\\Controllers\\DynamicFormController.cs", "ViewState": "AgIAAK0IAAAAAAAAAAAqwKAHAABfAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T02:50:23.159Z", "EditorCaption": ""}]}, {"DockedWidth": 1546, "SelectedChildIndex": -1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{34e76e81-ee4a-11d0-ae2e-00a0c90fffc3}"}]}]}]}