using System;
using System.Data;
using System.Threading.Tasks;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Logging;
using Webaby;
using Webaby.Data;
using Webaby.Core.DynamicForm;
using LinqToDB;
using LinqToDB.EntityFrameworkCore;
using System.Linq;

namespace Webaby.Core.DynamicForm.Queries
{
    public class GetDynamicFieldSectionDropdownListQuery : QueryBase<DynamicFieldSectionDropdown>
    {
        public Guid DynamicFormId { get; set; }
    }

    internal class GetDynamicFieldSectionDropdownListQueryHandler : QueryHandlerBase<GetDynamicFieldSectionDropdownListQuery, DynamicFieldSectionDropdown>
    {
        private readonly ILogger<GetDynamicFieldSectionDropdownListQueryHandler> _logger;
        private readonly DatabaseContext _dbContext;

        public GetDynamicFieldSectionDropdownListQueryHandler(IServiceProvider serviceProvider, ILogger<GetDynamicFieldSectionDropdownListQueryHandler> logger, DatabaseContext dbContext)
            : base(serviceProvider)
        {
            _logger = logger;
            _dbContext = dbContext;
        }

        /// <summary>
        /// Convert stored procedure GetDynamicFieldSectionDropdownList sang LinqToDB với recursive CTE
        /// Tham khảo pattern từ GetOrganizationTreeQuery
        /// </summary>
        public override async Task<QueryResult<DynamicFieldSectionDropdown>> ExecuteAsync(GetDynamicFieldSectionDropdownListQuery query)
        {
            // Tạo LinqToDB context từ EF DbContext
            var linq2dbCtx = _dbContext.CreateLinqToDBContext();

            // Truy cập bảng DynamicFieldSection
            var sectionTable = EntitySet.Get<DynamicFieldSectionEntity>();

            // Implement recursive CTE giống stored procedure
            var hierarchyCte = linq2dbCtx.GetCte<DynamicFieldSectionDropdown>(cte =>
            {
                // Base case: Root sections (ParentSectionId IS NULL)
                var baseQuery = sectionTable
                    .Where(s => s.DynamicFormId == query.DynamicFormId &&
                               !s.Deleted &&
                               s.ParentSectionId == null)
                    .Select(s => new DynamicFieldSectionDropdown
                    {
                        Id = s.Id,
                        SectionName = s.SectionName,
                        DisplayOrder = s.DisplayOrder,
                        ParentSectionId = s.ParentSectionId,
                        // SortPath: RIGHT('0000' + CAST(DisplayOrder AS VARCHAR), 4)
                        SortPath = Sql.Right("0000" + s.DisplayOrder.ToString(), 4),
                        Level = 0
                    });

                // Recursive case: Child sections
                var recursiveQuery =
                    from child in sectionTable
                    join parent in cte on child.ParentSectionId equals parent.Id
                    where child.DynamicFormId == query.DynamicFormId && !child.Deleted
                    select new DynamicFieldSectionDropdown
                    {
                        Id = child.Id,
                        SectionName = child.SectionName,
                        DisplayOrder = child.DisplayOrder,
                        ParentSectionId = child.ParentSectionId,
                        // SortPath: h.SortPath + '-' + RIGHT('0000' + CAST(d.DisplayOrder AS VARCHAR), 4)
                        SortPath = parent.SortPath + "-" + Sql.Right("0000" + child.DisplayOrder.ToString(), 4),
                        Level = parent.Level + 1
                    };

                return baseQuery.Concat(recursiveQuery);
            });

            // Lấy kết quả và sắp xếp theo SortPath như trong stored procedure
            var result = await hierarchyCte
                .OrderBy(h => h.SortPath)
                .ToListAsync();
             

            return QueryResult.Create(result);
        }
    }

    public class DynamicFieldSectionDropdown
    {
        public Guid Id { get; set; }
        public string SectionName { get; set; }
        public int? DisplayOrder { get; set; }
        public Guid? ParentSectionId { get; set; }
        public int Level { get; set; }
        public string SortPath { get; set; }
    }
}
