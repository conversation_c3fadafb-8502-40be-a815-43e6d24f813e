# So sánh SQL: Stored Procedure vs LinqToDB Generated

## Stored Procedure gốc

```sql
CREATE PROCEDURE [dbo].[SearchServiceTypeTaskTypeByDueTimeId]
    @dueTimeId UNIQUEIDENTIFIER NULL,
    @DataAuthorizedId UNIQUEIDENTIFIER,
    @startRow int,
    @endRow INT
AS 
BEGIN 
    WITH result AS	
    (
        SELECT  *,  row_number() over (order by (select 1)) RowNumber
        FROM	(
                    --Type 0 is ServiceType
                    SELECT (IIF(sc1.Name IS NOT NULL,sc1.Name, '') +  IIF(sc2.Name IS NOT NULL,'-' + sc2.Name, '') + IIF(sc3.Name IS NOT NULL,'-' + sc3.Name, '') + IIF(sc4.Name IS NOT NULL,'-' + sc4.Name, ''))collate SQL_Latin1_General_CP1_CI_AS AS Name , st.ProcessDueTimeId, st.AcceptDueTimeId , 0 AS Type, st.Id
                    FROM dbo.ServiceType st 
                        INNER JOIN dbo.DueTime dt ON st.ProcessDueTimeId = dt.Id 
                        LEFT JOIN dbo.ServiceCategory sc1 ON sc1.Id = st.Level1Id
                        LEFT JOIN dbo.ServiceCategory sc2 ON sc2.Id = st.Level2Id
                        LEFT JOIN dbo.ServiceCategory sc3 ON sc3.Id = st.Level3Id
                        LEFT JOIN dbo.ServiceCategory sc4 ON sc4.Id = st.Level4Id
                    WHERE	(@DataAuthorizedId IS NULL OR st.DataAuthorizedId = @DataAuthorizedId)
                            AND (st.ProcessDueTimeId = @dueTimeId OR st.AcceptDueTimeId = @dueTimeId)

                    UNION
                    --Type 1 is TaskType
                    SELECT	tt.TaskType,tt.ProcessDueTimeId,tt.AcceptDueTimeId, 1 AS Type, tt.Id
                    FROM	dbo.TaskType tt 
                            INNER JOIN dbo.DueTime dt ON tt.ProcessDueTimeId = dt.Id
                    WHERE	tt.ProcessDueTimeId = @dueTimeId OR tt.AcceptDueTimeId = @dueTimeId
                ) AS queryResult
    )
    SELECT  *, (SELECT  COUNT (*) FROM  result) TotalCount
    FROM  result
    WHERE  RowNumber BETWEEN  @startRow AND  @endRow
END
```

## LinqToDB Generated SQL (Dự kiến)

Với LinqToDB implementation mới, SQL được generate sẽ tương tự như:

```sql
-- LinqToDB sẽ generate SQL tương tự như sau:
WITH result AS (
    SELECT 
        *,
        ROW_NUMBER() OVER (ORDER BY (SELECT 1)) AS RowNumber
    FROM (
        -- ServiceType part (Type = 0)
        SELECT 
            st.Id,
            COALESCE(sc1.Name, '') + 
            CASE WHEN sc2.Name IS NOT NULL THEN '-' + sc2.Name ELSE '' END +
            CASE WHEN sc3.Name IS NOT NULL THEN '-' + sc3.Name ELSE '' END +
            CASE WHEN sc4.Name IS NOT NULL THEN '-' + sc4.Name ELSE '' END AS Name,
            st.ProcessDueTimeId,
            st.AcceptDueTimeId,
            0 AS Type
        FROM dbo.ServiceType st
        INNER JOIN dbo.DueTime dt ON st.ProcessDueTimeId = dt.Id
        LEFT JOIN dbo.ServiceCategory sc1 ON st.Level1Id = sc1.Id
        LEFT JOIN dbo.ServiceCategory sc2 ON st.Level2Id = sc2.Id
        LEFT JOIN dbo.ServiceCategory sc3 ON st.Level3Id = sc3.Id
        LEFT JOIN dbo.ServiceCategory sc4 ON st.Level4Id = sc4.Id
        WHERE 
            (@DataAuthorizedId IS NULL OR st.DataAuthorizedId = @DataAuthorizedId)
            AND (st.ProcessDueTimeId = @dueTimeId OR st.AcceptDueTimeId = @dueTimeId)
            AND st.Deleted = 0

        UNION

        -- TaskType part (Type = 1)
        SELECT 
            tt.Id,
            tt.TaskType AS Name,
            tt.ProcessDueTimeId,
            tt.AcceptDueTimeId,
            1 AS Type
        FROM dbo.TaskType tt
        INNER JOIN dbo.DueTime dt ON tt.ProcessDueTimeId = dt.Id
        WHERE 
            (tt.ProcessDueTimeId = @dueTimeId OR tt.AcceptDueTimeId = @dueTimeId)
            AND tt.Deleted = 0
    ) AS unionResult
)
SELECT 
    *,
    (SELECT COUNT(*) FROM result) AS TotalCount
FROM result
WHERE RowNumber BETWEEN @startRow AND @endRow
```

## Key Differences và Improvements

### ✅ Những gì đã match:

1. **CTE Structure**: Sử dụng CTE với ROW_NUMBER()
2. **UNION**: Kết hợp ServiceType và TaskType
3. **INNER JOIN với DueTime**: Cả hai parts đều có
4. **LEFT JOIN với ServiceCategory**: 4 levels
5. **WHERE conditions**: Logic filtering giống nhau
6. **Pagination**: BETWEEN startRow và endRow

### 🔧 Improvements trong LinqToDB version:

1. **Soft Delete**: Thêm `AND st.Deleted = 0` và `AND tt.Deleted = 0`
2. **Type Safety**: Compile-time checking
3. **Parameterization**: Tự động parameterize values
4. **Cross-Database**: Có thể chạy trên PostgreSQL

### ⚠️ Potential Differences:

1. **String Concatenation**: 
   - SP: `IIF(sc1.Name IS NOT NULL,sc1.Name, '') + IIF(...)`
   - LinqToDB: `COALESCE(sc1.Name, '') + CASE WHEN...`

2. **Collation**: 
   - SP: `collate SQL_Latin1_General_CP1_CI_AS`
   - LinqToDB: Sử dụng default collation

3. **Order**: 
   - SP: `ORDER BY (SELECT 1)` - random order
   - LinqToDB: Có thể generate order khác

## Testing SQL Generation

Để xem SQL thực tế được generate bởi LinqToDB:

```csharp
public async Task<string> GetGeneratedSQL(GetServiceTypeTaskTypeByDueTimeIdLinqToDBQuery query)
{
    var dataAuthorizedId = _userService.GetDataAuthorizedId();
    
    // Build query như trong handler
    var serviceTypeQuery = (
        from st in EntitySet.Get<ServiceTypeEntity>().ToLinqToDB()
        join dt in EntitySet.Get<DueTimeEntity>().ToLinqToDB() on st.ProcessDueTimeId equals dt.Id
        // ... rest of query
        select new ServiceTypeTaskTypeByDueTimeIdData { ... }
    );

    var taskTypeQuery = (
        from tt in EntitySet.Get<TaskTypeEntity>().ToLinqToDB()
        join dt in EntitySet.Get<DueTimeEntity>().ToLinqToDB() on tt.ProcessDueTimeId equals dt.Id
        // ... rest of query
        select new ServiceTypeTaskTypeByDueTimeIdData { ... }
    );

    var unionQuery = serviceTypeQuery.Union(taskTypeQuery);
    
    // Get generated SQL
    var sql = unionQuery.ToString();
    return sql;
}
```

## Performance Comparison

### Stored Procedure Advantages:
- **Pre-compiled**: Execution plan cached
- **Optimized**: Hand-tuned SQL
- **Less overhead**: Direct execution

### LinqToDB Advantages:
- **Query plan caching**: LinqToDB caches generated SQL
- **Parameter sniffing**: Better parameter handling
- **Adaptive**: Can optimize based on data distribution

## Recommendations

1. **Benchmark both versions** với real data
2. **Compare execution plans** trong SQL Server Management Studio
3. **Monitor performance** trong production
4. **A/B test** để đảm bảo correctness

## Conclusion

LinqToDB version được thiết kế để generate SQL **rất gần** với stored procedure gốc. Sự khác biệt chính là:

- Thêm soft delete checks
- Có thể có syntax khác nhau cho string concatenation
- Order có thể khác (nhưng không ảnh hưởng kết quả với pagination)

Về mặt logic và performance, hai versions sẽ tương đương nhau.
