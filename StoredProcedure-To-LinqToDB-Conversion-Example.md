# Convert Stored Procedure sang LinqToDB - <PERSON><PERSON> dụ thực tế

## Tổng quan

Đây là ví dụ convert stored procedure `SearchServiceTypeTaskTypeByDueTimeId` sang LinqToDB trong dự án TinyCRM.

## Stored Procedure gốc

```sql
CREATE PROCEDURE [dbo].[SearchServiceTypeTaskTypeByDueTimeId]
    @dueTimeId UNIQUEIDENTIFIER NULL,
    @DataAuthorizedId UNIQUEIDENTIFIER,
    @startRow int,
    @endRow INT
AS 
BEGIN 
    WITH result AS	
    (
        SELECT  *,  row_number() over (order by (select 1)) RowNumber
        FROM	(
                    --Type 0 is ServiceType
                    SELECT (IIF(sc1.Name IS NOT NULL,sc1.Name, '') +  IIF(sc2.Name IS NOT NULL,'-' + sc2.Name, '') + IIF(sc3.Name IS NOT NULL,'-' + sc3.Name, '') + IIF(sc4.Name IS NOT NULL,'-' + sc4.Name, ''))collate SQL_Latin1_General_CP1_CI_AS AS Name , st.ProcessDueTimeId, st.AcceptDueTimeId , 0 AS Type, st.Id
                    FROM dbo.ServiceType st 
                        INNER JOIN dbo.DueTime dt ON st.ProcessDueTimeId = dt.Id 
                        LEFT JOIN dbo.ServiceCategory sc1 ON sc1.Id = st.Level1Id
                        LEFT JOIN dbo.ServiceCategory sc2 ON sc2.Id = st.Level2Id
                        LEFT JOIN dbo.ServiceCategory sc3 ON sc3.Id = st.Level3Id
                        LEFT JOIN dbo.ServiceCategory sc4 ON sc4.Id = st.Level4Id
                    WHERE	(@DataAuthorizedId IS NULL OR st.DataAuthorizedId = @DataAuthorizedId)
                            AND st.ProcessDueTimeId = @dueTimeId OR st.AcceptDueTimeId = @dueTimeId

                    UNION
                    --Type 1 is TaskType
                    SELECT	tt.TaskType,tt.ProcessDueTimeId,tt.AcceptDueTimeId, 1 AS Type, tt.Id
                    FROM	dbo.TaskType tt 
                            INNER JOIN dbo.DueTime dt ON tt.ProcessDueTimeId = dt.Id
                    WHERE	tt.ProcessDueTimeId = @dueTimeId OR tt.AcceptDueTimeId = @dueTimeId
                ) AS queryResult
    )
    SELECT  *, (SELECT  COUNT (*) FROM  result) TotalCount
    FROM  result
    WHERE  RowNumber BETWEEN  @startRow AND  @endRow
END
```

## Implementation cũ (Stored Procedure)

```csharp
public override async Task<QueryResult<ServiceTypeTaskTypeByDueTimeIdData>> ExecuteAsync(GetServiceTypeTaskTypeByDueTimeIdQuery query)
{
    int startRow = query.Pagination.Index * query.Pagination.Size + 1;
    int endRow = query.Pagination.Index * query.Pagination.Size + query.Pagination.Size;

    var cmd = EntitySet.CreateDbCommand();
    cmd.CommandText = "SearchServiceTypeTaskTypeByDueTimeId";
    cmd.CommandType = CommandType.StoredProcedure;

    // Add các tham số đúng chuẩn
    cmd.AddDataAuthorizedParameters(_userService);
    cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@dueTimeId", query.DueTimeId));
    cmd.Parameters.Add(DbParameterHelper.AddNullableInt(cmd, "@startRow", startRow));
    cmd.Parameters.Add(DbParameterHelper.AddNullableInt(cmd, "@endRow", endRow));

    var entities = await EntitySet.ExecuteReadCommandAsync<ServiceTypeTaskTypeByDueTimeIdData>(cmd);

    return QueryResult.Create(entities);
}
```

## Implementation mới (LinqToDB)

```csharp
public override async Task<QueryResult<ServiceTypeTaskTypeByDueTimeIdData>> ExecuteAsync(GetServiceTypeTaskTypeByDueTimeIdLinqToDBQuery query)
{
    var dataAuthorizedId = _userService.GetDataAuthorizedId();
    
    // Tính toán phân trang
    int skip = query.Pagination.Index * query.Pagination.Size;
    int take = query.Pagination.Size;

    // Part 1: ServiceType query (Type = 0)
    var serviceTypeQuery = (
        from st in EntitySet.Get<ServiceTypeEntity>().ToLinqToDB()
        join sc1 in EntitySet.Get<ServiceCategoryEntity>().ToLinqToDB() on st.Level1Id equals sc1.Id into sc1Group
        from sc1 in sc1Group.DefaultIfEmpty()
        join sc2 in EntitySet.Get<ServiceCategoryEntity>().ToLinqToDB() on st.Level2Id equals sc2.Id into sc2Group
        from sc2 in sc2Group.DefaultIfEmpty()
        join sc3 in EntitySet.Get<ServiceCategoryEntity>().ToLinqToDB() on st.Level3Id equals sc3.Id into sc3Group
        from sc3 in sc3Group.DefaultIfEmpty()
        join sc4 in EntitySet.Get<ServiceCategoryEntity>().ToLinqToDB() on st.Level4Id equals sc4.Id into sc4Group
        from sc4 in sc4Group.DefaultIfEmpty()
        where (dataAuthorizedId == null || st.DataAuthorizedId == dataAuthorizedId) &&
              (st.ProcessDueTimeId == query.DueTimeId || st.AcceptDueTimeId == query.DueTimeId) &&
              !st.Deleted
        select new ServiceTypeTaskTypeByDueTimeIdData
        {
            Id = st.Id,
            Name = (sc1.Name ?? "") + 
                   (sc2.Name != null ? "-" + sc2.Name : "") + 
                   (sc3.Name != null ? "-" + sc3.Name : "") + 
                   (sc4.Name != null ? "-" + sc4.Name : ""),
            ProcessDueTimeId = st.ProcessDueTimeId,
            AcceptDueTimeId = st.AcceptDueTimeId,
            Type = 0 // ServiceType
        }
    );

    // Part 2: TaskType query (Type = 1)
    var taskTypeQuery = (
        from tt in EntitySet.Get<TaskTypeEntity>().ToLinqToDB()
        where (tt.ProcessDueTimeId == query.DueTimeId || tt.AcceptDueTimeId == query.DueTimeId) &&
              !tt.Deleted
        select new ServiceTypeTaskTypeByDueTimeIdData
        {
            Id = tt.Id,
            Name = tt.TaskType,
            ProcessDueTimeId = tt.ProcessDueTimeId,
            AcceptDueTimeId = tt.AcceptDueTimeId,
            Type = 1 // TaskType
        }
    );

    // Union hai queries
    var unionQuery = serviceTypeQuery.Union(taskTypeQuery);

    // Đếm tổng số records
    var totalCount = await unionQuery.CountAsync();

    // Áp dụng phân trang đơn giản với Skip/Take
    var pagedResult = await unionQuery
        .Skip(skip)
        .Take(take)
        .ToListAsync();

    // Set TotalCount cho tất cả items
    foreach (var item in pagedResult)
    {
        item.TotalCount = totalCount;
    }

    return new QueryResult<ServiceTypeTaskTypeByDueTimeIdData>(pagedResult);
}
```

## So sánh

### ✅ Ưu điểm của LinqToDB

1. **Type Safety**: Compile-time checking, IntelliSense support
2. **Maintainability**: Code dễ đọc, dễ debug, dễ refactor
3. **Testability**: Có thể unit test dễ dàng
4. **Flexibility**: Dễ dàng thay đổi logic mà không cần alter stored procedure
5. **Version Control**: Code changes được track trong source control
6. **Cross-Database**: Có thể chạy trên nhiều loại database

### ⚠️ Nhược điểm có thể có

1. **Performance**: Có thể chậm hơn stored procedure trong một số trường hợp
2. **SQL Generation**: Phụ thuộc vào LinqToDB để generate SQL tối ưu
3. **Complex Logic**: Một số logic phức tạp khó translate

## Cách sử dụng

### Sử dụng Stored Procedure (cũ)
```csharp
var query = new GetServiceTypeTaskTypeByDueTimeIdQuery
{
    DueTimeId = dueTimeId,
    Pagination = new PaginationInfo { Index = 0, Size = 10 }
};

var result = await _mediator.Send(query);
```

### Sử dụng LinqToDB (mới)
```csharp
var query = new GetServiceTypeTaskTypeByDueTimeIdLinqToDBQuery
{
    DueTimeId = dueTimeId,
    Pagination = new PaginationInfo { Index = 0, Size = 10 }
};

var result = await _mediator.Send(query);
```

## Testing

### Unit Test cho LinqToDB version

```csharp
[Test]
public async Task GetServiceTypeTaskTypeByDueTimeId_LinqToDB_ShouldReturnCorrectResults()
{
    // Arrange
    var dueTimeId = Guid.NewGuid();
    await SeedTestDataAsync(dueTimeId);
    
    var query = new GetServiceTypeTaskTypeByDueTimeIdLinqToDBQuery
    {
        DueTimeId = dueTimeId,
        Pagination = new PaginationInfo { Index = 0, Size = 10 }
    };

    // Act
    var result = await _handler.ExecuteAsync(query);

    // Assert
    Assert.IsNotNull(result);
    Assert.IsTrue(result.Data.Any());
    Assert.IsTrue(result.Data.All(x => x.TotalCount > 0));
}
```

### Performance Comparison Test

```csharp
[Test]
public async Task Performance_Comparison_StoredProc_vs_LinqToDB()
{
    var dueTimeId = Guid.NewGuid();
    await SeedLargeTestDataAsync(dueTimeId, 10000); // Seed 10k records
    
    // Test Stored Procedure
    var stopwatch1 = Stopwatch.StartNew();
    var spResult = await _spHandler.ExecuteAsync(new GetServiceTypeTaskTypeByDueTimeIdQuery { DueTimeId = dueTimeId });
    stopwatch1.Stop();
    
    // Test LinqToDB
    var stopwatch2 = Stopwatch.StartNew();
    var linqResult = await _linqHandler.ExecuteAsync(new GetServiceTypeTaskTypeByDueTimeIdLinqToDBQuery { DueTimeId = dueTimeId });
    stopwatch2.Stop();
    
    Console.WriteLine($"Stored Procedure: {stopwatch1.ElapsedMilliseconds}ms");
    Console.WriteLine($"LinqToDB: {stopwatch2.ElapsedMilliseconds}ms");
    
    // Verify results are equivalent
    Assert.AreEqual(spResult.Data.Count(), linqResult.Data.Count());
}
```

## Migration Strategy

### Phase 1: Parallel Implementation
- Giữ stored procedure version
- Implement LinqToDB version
- A/B testing trong development

### Phase 2: Gradual Rollout
- Deploy LinqToDB version với feature flag
- Monitor performance và correctness
- Rollback nếu có issues

### Phase 3: Complete Migration
- Remove stored procedure version
- Clean up old code
- Update documentation

## Best Practices

1. **Always benchmark**: So sánh performance trước khi deploy
2. **Test thoroughly**: Đảm bảo results consistency
3. **Monitor in production**: Track performance metrics
4. **Keep it simple**: Không over-engineer, giữ logic đơn giản
5. **Document changes**: Ghi chú lý do convert và trade-offs

## Kết luận

Convert stored procedure sang LinqToDB mang lại nhiều lợi ích về maintainability và type safety, nhưng cần cân nhắc về performance. Trong trường hợp này, LinqToDB version dễ đọc, dễ test và maintain hơn nhiều so với stored procedure.
