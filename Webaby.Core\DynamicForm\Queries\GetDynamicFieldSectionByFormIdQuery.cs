﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Webaby.Data;
using Webaby.Localization;

namespace Webaby.Core.DynamicForm.Queries
{
    public class GetDynamicFieldSectionByFormIdQuery : QueryBase<DynamicFieldSectionData>
    {
        public Guid DynamicFormId { get; set; }
    }

    internal class GetDynamicFieldSectionByFormIdQueryHandler : QueryHandlerBase<GetDynamicFieldSectionByFormIdQuery, DynamicFieldSectionData>
    {
        public GetDynamicFieldSectionByFormIdQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<DynamicFieldSectionData>> ExecuteAsync(GetDynamicFieldSectionByFormIdQuery query)
        {
            var dynamicFieldSectionEntities = await (from form in EntitySet.Get<DynamicFormEntity>()
                                                     join section in EntitySet.Get<DynamicFieldSectionEntity>() on form.Id equals section.DynamicFormId
                                                     where section.DynamicFormId == query.DynamicFormId
                                                     orderby section.DisplayOrder
                                                     select section).ToListAsync();

            var dynamicFieldSections = dynamicFieldSectionEntities.Select(section => Mapper.Map<DynamicFieldSectionData>(section)).ToList();
            return QueryResult.Create(dynamicFieldSections);
        }
    }
}