﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Logging;
using Webaby;
using Webaby.Data;
using Webaby.Security;

namespace Webaby.Core.DynamicForm.Commands
{
    public class SaveDynamicFieldOrdersCommand : CommandBase
    {
        public Guid DynamicFormId { get; set; }
        public List<DynamicFieldOrder> DynamicFieldOrders { get; set; }
    }

    public class DynamicFieldOrder
    {
        public Guid DynamicFieldId { get; set; }
        public int Order { get; set; }
        public Guid? DynamicFieldSectionId { get; set; }
    }

    internal class SaveDynamicFieldOrdersCommandHandler : CommandHandlerBase<SaveDynamicFieldOrdersCommand>
    {
        private readonly IUserService _userService;
        private readonly ILogger<SaveDynamicFieldOrdersCommandHandler> _logger;

        public SaveDynamicFieldOrdersCommandHandler(IServiceProvider serviceProvider, IUserService userService, ILogger<SaveDynamicFieldOrdersCommandHandler> logger)
            : base(serviceProvider)
        {
            _userService = userService;
            _logger = logger;
        }

        /// <summary><PERSON><PERSON><PERSON> thứ tự các trường động của biểu mẫu</summary>
        public override async Task ExecuteAsync(SaveDynamicFieldOrdersCommand command)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.CommandText = "dbo.SaveDynamicFieldOrders";
            cmd.CommandType = CommandType.StoredProcedure;

            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@DynamicFormId", command.DynamicFormId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@UserId", _userService.GetCurrentUser().Id));

            var tvp = new DataTable();
            tvp.Columns.Add("Id", typeof(Guid));
            tvp.Columns.Add("SortOrder", typeof(int));
            tvp.Columns.Add("DynamicFieldSectionId", typeof(Guid));

            foreach (var item in command.DynamicFieldOrders)
            {
                var field = await EntitySet.GetAsync<DynamicFieldDefinitionEntity>(item.DynamicFieldId);
                if (field != null && field.FieldType == FieldType.Calculated)
                {
                    if (field.FomularByFieldName.IsNullOrEmpty() && !string.IsNullOrEmpty(field.DefaultValue))
                    {
                        var pattern = new Regex(@"B(?<order>\d+)$", RegexOptions.Compiled);
                        foreach (Match match in pattern.Matches(field.DefaultValue))
                        {
                            if (int.TryParse(match.Groups["order"].Value, out int order) && order == item.Order)
                            {
                                throw new InvalidOperationException(T["Thứ tự làm công thức bị lặp vô hạn!"]);
                            }
                        }
                    }
                }

                tvp.Rows.Add(item.DynamicFieldId, item.Order, item.DynamicFieldSectionId);
            }

            var sqlParameterTvp = new SqlParameter("@DynamicFieldOrders", SqlDbType.Structured)
            {
                TypeName = "dbo.SortIdListSectionId",
                Value = tvp
            };
            cmd.Parameters.Add(sqlParameterTvp);

            await EntitySet.ExecuteNonQueryAsync(cmd);
        }
    }
}
