# Convert GetDynamicFieldSectionDropdownList Stored Procedure sang LinqToDB

## Tổng quan

Đây là ví dụ convert stored procedure `GetDynamicFieldSectionDropdownList` sang LinqToDB sử dụng recursive CTE, tham khảo pattern từ `GetOrganizationTreeQuery.cs`.

## Stored Procedure gốc

```sql
CREATE PROCEDURE [dbo].[GetDynamicFieldSectionDropdownList]
    @DynamicFormId UNIQUEIDENTIFIER
AS BEGIN
    WITH DynamicFieldSectionHierarchy AS 
    (
        -- Base case: Root sections
        SELECT Id,
                SectionName,
                DisplayOrder,
                ParentSectionId,
                CAST(RIGHT('0000' + CAST(DisplayOrder AS VARCHAR), 4) AS VARCHAR) AS SortPath,
                0 AS Level
        FROM	dbo.DynamicFieldSection
        WHERE	DynamicFormId = @DynamicFormId
                AND Deleted = 0
                AND ParentSectionId IS NULL
        
        UNION ALL
        
        -- Recursive case: Child sections
        SELECT	d.Id,
                d.<PERSON>,
                d.<PERSON><PERSON><PERSON><PERSON><PERSON>,
                d.Parent<PERSON>ectionId,
                CAST(h.SortPath + '-' + RIGHT('0000' + CAST(d.DisplayOrder AS VARCHAR), 4) AS VARCHAR) AS SortPath,
                h.Level + 1
        FROM	dbo.DynamicFieldSection d
                JOIN DynamicFieldSectionHierarchy h ON d.ParentSectionId = h.Id
        WHERE	d.DynamicFormId = @DynamicFormId
                AND d.Deleted = 0
    )
    SELECT	*
    FROM	DynamicFieldSectionHierarchy
    ORDER BY SortPath;
END
```

## Implementation cũ (Stored Procedure)

```csharp
public override async Task<QueryResult<DynamicFieldSectionDropdown>> ExecuteAsync(GetDynamicFieldSectionDropdownListQuery query)
{
    var cmd = EntitySet.CreateDbCommand();
    cmd.CommandText = "dbo.GetDynamicFieldSectionDropdownList";
    cmd.CommandType = CommandType.StoredProcedure;

    cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@DynamicFormId", query.DynamicFormId));

    var entities = await EntitySet.ExecuteReadCommandAsync<DynamicFieldSectionDropdown>(cmd);

    return QueryResult.Create(entities);
}
```

## Implementation mới (LinqToDB với Recursive CTE)

```csharp
public override async Task<QueryResult<DynamicFieldSectionDropdown>> ExecuteAsync(GetDynamicFieldSectionDropdownListQuery query)
{
    // Tạo LinqToDB context từ EF DbContext
    var linq2dbCtx = _dbContext.CreateLinqToDBContext();

    // Truy cập bảng DynamicFieldSection
    var sectionTable = EntitySet.Get<DynamicFieldSectionEntity>();

    // Implement recursive CTE giống stored procedure
    var hierarchyCte = linq2dbCtx.GetCte<DynamicFieldSectionDropdown>(cte =>
    {
        // Base case: Root sections (ParentSectionId IS NULL)
        var baseQuery = sectionTable
            .Where(s => s.DynamicFormId == query.DynamicFormId && 
                       !s.Deleted && 
                       s.ParentSectionId == null)
            .Select(s => new DynamicFieldSectionDropdown
            {
                Id = s.Id,
                SectionName = s.SectionName,
                DisplayOrder = s.DisplayOrder,
                ParentSectionId = s.ParentSectionId,
                // SortPath: RIGHT('0000' + CAST(DisplayOrder AS VARCHAR), 4)
                SortPath = Sql.Right("0000" + s.DisplayOrder.ToString(), 4),
                Level = 0
            });

        // Recursive case: Child sections
        var recursiveQuery = 
            from child in sectionTable
            join parent in cte on child.ParentSectionId equals parent.Id
            where child.DynamicFormId == query.DynamicFormId && !child.Deleted
            select new DynamicFieldSectionDropdown
            {
                Id = child.Id,
                SectionName = child.SectionName,
                DisplayOrder = child.DisplayOrder,
                ParentSectionId = child.ParentSectionId,
                // SortPath: h.SortPath + '-' + RIGHT('0000' + CAST(d.DisplayOrder AS VARCHAR), 4)
                SortPath = parent.SortPath + "-" + Sql.Right("0000" + child.DisplayOrder.ToString(), 4),
                Level = parent.Level + 1
            };

        return baseQuery.Concat(recursiveQuery);
    });

    // Lấy kết quả và sắp xếp theo SortPath như trong stored procedure
    var result = await hierarchyCte
        .OrderBy(h => h.SortPath)
        .ToListAsync();

    return QueryResult.Create(result);
}
```

## Key Features của LinqToDB Implementation

### ✅ **Recursive CTE Pattern**

Tham khảo từ `GetOrganizationTreeQuery.cs`:

```csharp
// Pattern từ GetOrganizationTreeQuery
var allOrgsCte = linq2dbCtx.GetCte<OrgTreeItem>(cte =>
{
    var baseQuery = orgTable
        .Where(o => o.ParentId == null)
        .Select(o => new OrgTreeItem { ... });
        
    var recursiveQuery = 
        from c in orgTable
        join p in cte on c.ParentId equals p.OrgId
        select new OrgTreeItem { ... };
        
    return baseQuery.Concat(recursiveQuery);
});

// Áp dụng cho DynamicFieldSection
var hierarchyCte = linq2dbCtx.GetCte<DynamicFieldSectionDropdown>(cte =>
{
    var baseQuery = sectionTable
        .Where(s => s.ParentSectionId == null)
        .Select(s => new DynamicFieldSectionDropdown { ... });
        
    var recursiveQuery = 
        from child in sectionTable
        join parent in cte on child.ParentSectionId equals parent.Id
        select new DynamicFieldSectionDropdown { ... };
        
    return baseQuery.Concat(recursiveQuery);
});
```

### 🔧 **SortPath Logic**

Match chính xác với stored procedure:

```sql
-- Stored Procedure
RIGHT('0000' + CAST(DisplayOrder AS VARCHAR), 4)
h.SortPath + '-' + RIGHT('0000' + CAST(d.DisplayOrder AS VARCHAR), 4)
```

```csharp
// LinqToDB
Sql.Right("0000" + s.DisplayOrder.ToString(), 4)
parent.SortPath + "-" + Sql.Right("0000" + child.DisplayOrder.ToString(), 4)
```

### 📊 **SQL được generate**

LinqToDB sẽ generate SQL tương tự:

```sql
WITH DynamicFieldSectionHierarchy AS (
    -- Base case
    SELECT 
        s.Id,
        s.SectionName,
        s.DisplayOrder,
        s.ParentSectionId,
        RIGHT('0000' + CAST(s.DisplayOrder AS VARCHAR), 4) AS SortPath,
        0 AS Level
    FROM DynamicFieldSection s
    WHERE s.DynamicFormId = @DynamicFormId 
        AND s.Deleted = 0 
        AND s.ParentSectionId IS NULL

    UNION ALL

    -- Recursive case
    SELECT 
        child.Id,
        child.SectionName,
        child.DisplayOrder,
        child.ParentSectionId,
        parent.SortPath + '-' + RIGHT('0000' + CAST(child.DisplayOrder AS VARCHAR), 4) AS SortPath,
        parent.Level + 1
    FROM DynamicFieldSection child
    INNER JOIN DynamicFieldSectionHierarchy parent ON child.ParentSectionId = parent.Id
    WHERE child.DynamicFormId = @DynamicFormId 
        AND child.Deleted = 0
)
SELECT *
FROM DynamicFieldSectionHierarchy
ORDER BY SortPath
```

## So sánh với GetOrganizationTreeQuery

| Aspect | GetOrganizationTreeQuery | GetDynamicFieldSectionDropdownList |
|--------|--------------------------|-------------------------------------|
| **CTE Pattern** | ✅ `linq2dbCtx.GetCte<>()` | ✅ `linq2dbCtx.GetCte<>()` |
| **Base Case** | `ParentId == null` | `ParentSectionId == null` |
| **Recursive Join** | `c.ParentId equals p.OrgId` | `child.ParentSectionId equals parent.Id` |
| **Hierarchy Field** | `NodeLevel` | `Level` |
| **Sorting** | `OrderInGroup` | `SortPath` |
| **Filtering** | Organization type, Province | DynamicFormId, Deleted |

## Ưu điểm của LinqToDB version

### 🚀 **Performance & Maintainability**
- **Type Safety**: Compile-time checking
- **Readable Code**: Dễ đọc và hiểu
- **Testable**: Có thể unit test
- **Cross-Database**: Hoạt động với PostgreSQL

### 🔧 **Flexibility**
- **Easy Debugging**: Có thể debug từng step
- **Extensible**: Dễ thêm logic filtering
- **Reusable Pattern**: Có thể áp dụng cho hierarchical data khác

## Testing

### Unit Test

```csharp
[Test]
public async Task GetDynamicFieldSectionDropdownList_LinqToDB_ShouldReturnHierarchy()
{
    // Arrange
    var formId = Guid.NewGuid();
    await SeedHierarchicalSectionsAsync(formId);
    
    var query = new GetDynamicFieldSectionDropdownListQuery { DynamicFormId = formId };

    // Act
    var result = await _handler.ExecuteAsync(query);

    // Assert
    Assert.IsNotNull(result);
    Assert.IsTrue(result.Data.Any());
    
    // Verify hierarchy structure
    var rootSections = result.Data.Where(x => x.Level == 0).ToList();
    var childSections = result.Data.Where(x => x.Level > 0).ToList();
    
    Assert.IsTrue(rootSections.Any());
    Assert.IsTrue(childSections.All(c => c.ParentSectionId != null));
    
    // Verify SortPath ordering
    var sortedResult = result.Data.OrderBy(x => x.SortPath).ToList();
    CollectionAssert.AreEqual(result.Data.ToList(), sortedResult);
}
```

### Performance Comparison

```csharp
[Test]
public async Task Performance_Comparison_StoredProc_vs_LinqToDB()
{
    var formId = Guid.NewGuid();
    await SeedLargeHierarchyAsync(formId, 1000); // 1000 sections
    
    // Test Stored Procedure
    var stopwatch1 = Stopwatch.StartNew();
    var spResult = await _spHandler.ExecuteAsync(new GetDynamicFieldSectionDropdownListQuery { DynamicFormId = formId });
    stopwatch1.Stop();
    
    // Test LinqToDB
    var stopwatch2 = Stopwatch.StartNew();
    var linqResult = await _linqHandler.ExecuteAsync(new GetDynamicFieldSectionDropdownListQuery { DynamicFormId = formId });
    stopwatch2.Stop();
    
    Console.WriteLine($"Stored Procedure: {stopwatch1.ElapsedMilliseconds}ms");
    Console.WriteLine($"LinqToDB: {stopwatch2.ElapsedMilliseconds}ms");
    
    // Verify results are equivalent
    Assert.AreEqual(spResult.Data.Count(), linqResult.Data.Count());
}
```

## Kết luận

LinqToDB implementation với recursive CTE pattern từ `GetOrganizationTreeQuery.cs` cho phép:

1. **Match chính xác** logic của stored procedure
2. **Type safety** và maintainability tốt hơn
3. **Reusable pattern** cho hierarchical data khác
4. **Cross-database compatibility**

Pattern này có thể áp dụng cho bất kỳ hierarchical structure nào trong codebase TinyCRM.
