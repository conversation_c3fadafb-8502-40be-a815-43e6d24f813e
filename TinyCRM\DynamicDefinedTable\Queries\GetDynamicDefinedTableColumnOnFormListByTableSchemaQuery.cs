using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Webaby.Data;
using Webaby;
using System.Data;

namespace TinyCRM.DynamicDefinedTable.Queries
{
    public class GetDynamicDefinedTableColumnOnFormListByTableSchemaQuery : QueryBase<DynamicDefinedTableColumnData>
    {
        public Guid DynamicDefinedTableSchemaId { get; set; }

        public Guid? DynamicFieldId { get; set; }
    }

    internal class GetDynamicDefinedTableColumnOnFormListByTableSchemaQueryHandler : QueryHandlerBase<GetDynamicDefinedTableColumnOnFormListByTableSchemaQuery, DynamicDefinedTableColumnData>
    {
        public GetDynamicDefinedTableColumnOnFormListByTableSchemaQueryHandler(IServiceProvider serviceProvider)
            : base(serviceProvider) { }
        public override async Task<QueryResult<DynamicDefinedTableColumnData>> ExecuteAsync(GetDynamicDefinedTableColumnOnFormListByTableSchemaQuery query)
        {
            var sqlCommand = EntitySet.CreateDbCommand();
            sqlCommand.CommandType = CommandType.StoredProcedure;
            sqlCommand.CommandText = "dbo.GetDynamicDefinedTableColumnOnFormList";
            sqlCommand.Parameters.AddRange(new[]
            {
                DbParameterHelper.AddNullableGuid(sqlCommand, "@DynamicDefinedTableSchemaId", query.DynamicDefinedTableSchemaId),
                DbParameterHelper.AddNullableGuid(sqlCommand, "@DynamicFieldId", query.DynamicFieldId),
            });

            var mainQuery = await EntitySet.ExecuteReadCommandAsync<DynamicDefinedTableColumnData>(sqlCommand);
            return new QueryResult<DynamicDefinedTableColumnData>(mainQuery);
        }
    }
}