using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TinyCRM.ServiceCategory;
using TinyCRM.TaskType;
using TinyCRM.ServiceType;
using Webaby;
using Webaby.Core.DueTime.Queries;
using Webaby.Data;
using Microsoft.Extensions.Logging;
using Webaby.Security;
using Microsoft.EntityFrameworkCore;

namespace Webaby.Core.DueTime.Queries
{
    public class GetServiceTypeTaskTypeByDueTimeIdQuery : QueryBase<ServiceTypeTaskTypeByDueTimeIdData>
    {
        public Guid DueTimeId { get; set; }
    }

    internal class GetServiceTypeTaskTypeByDueTimeIdQueryHandler :
        QueryHandlerBase<GetServiceTypeTaskTypeByDueTimeIdQuery, ServiceTypeTaskTypeByDueTimeIdData>
    {
        private readonly ILogger<GetServiceTypeTaskTypeByDueTimeIdQueryHandler> _logger;
        IUserService _userService;

        public GetServiceTypeTaskTypeByDueTimeIdQueryHandler(
            IServiceProvider serviceProvider,
            IUserService userService,
            ILogger<GetServiceTypeTaskTypeByDueTimeIdQueryHandler> logger)
            : base(serviceProvider)
        {
            _logger = logger;
            _userService = userService;
        }

        /// <summary>
        /// Truy vấn danh sách ServiceTypeTaskType theo DueTimeId (có phân trang, dùng LINQ)
        /// </summary>
        public override async Task<QueryResult<ServiceTypeTaskTypeByDueTimeIdData>> ExecuteAsync(GetServiceTypeTaskTypeByDueTimeIdQuery query)
        {
            var dataAuthorizedId = _userService.GetCurrentUser();

            // LINQ equivalent of the stored procedure SearchServiceTypeTaskTypeByDueTimeId

            // First part: ServiceType query
            var serviceTypeQuery = from st in EntitySet.Get<ServiceTypeEntity>()
                                   join sc1 in EntitySet.Get<ServiceCategoryEntity>() on st.Level1Id equals sc1.Id into sc1Group
                                   from sc1 in sc1Group.DefaultIfEmpty()
                                   join sc2 in EntitySet.Get<ServiceCategoryEntity>() on st.Level2Id equals sc2.Id into sc2Group
                                   from sc2 in sc2Group.DefaultIfEmpty()
                                   join sc3 in EntitySet.Get<ServiceCategoryEntity>() on st.Level3Id equals sc3.Id into sc3Group
                                   from sc3 in sc3Group.DefaultIfEmpty()
                                   join sc4 in EntitySet.Get<ServiceCategoryEntity>() on st.Level4Id equals sc4.Id into sc4Group
                                   from sc4 in sc4Group.DefaultIfEmpty()
                                   where (query.DueTimeId == null ||
                                         st.ProcessDueTimeId == query.DueTimeId ||
                                         st.AcceptDueTimeId == query.DueTimeId) &&
                                         st.DataAuthorizedId == dataAuthorizedId
                                   select new ServiceTypeTaskTypeByDueTimeIdData
                                   {
                                       Id = st.Id,
                                       Name = (sc1.Name ?? "") + " / " + (sc2.Name ?? "") + " / " + (sc3.Name ?? "") + " / " + (sc4.Name ?? ""),
                                       ProcessDueTimeId = st.ProcessDueTimeId,
                                       AcceptDueTimeId = st.AcceptDueTimeId,
                                       Type = 1, // ServiceType
                                       TotalCount = 0 // Will be set later
                                   };

            // Second part: TaskType query
            var taskTypeQuery = from tt in EntitySet.Get<TaskTypeEntity>()
                                where (query.DueTimeId == null ||
                                      tt.ProcessDueTimeId == query.DueTimeId ||
                                      tt.AcceptDueTimeId == query.DueTimeId) &&
                                      tt.DataAuthorizedId == dataAuthorizedId
                                select new ServiceTypeTaskTypeByDueTimeIdData
                                {
                                    Id = tt.Id,
                                    Name = tt.Name,
                                    ProcessDueTimeId = tt.ProcessDueTimeId,
                                    AcceptDueTimeId = tt.AcceptDueTimeId,
                                    Type = 2, // TaskType
                                    TotalCount = 0 // Will be set later
                                };

            // Union the two queries
            var unionQuery = serviceTypeQuery.Union(taskTypeQuery);

            // Get total count
            var totalCount = await unionQuery.CountAsync();

            // Apply pagination
            var startRow = query.Pagination.Index * query.Pagination.Size;
            var takeRows = query.Pagination.Size;

            var results = await unionQuery
                .OrderBy(x => x.Name)
                .Skip(startRow)
                .Take(takeRows)
                .ToListAsync();

            // Set TotalCount for all items
            foreach (var item in results)
            {
                item.TotalCount = totalCount;
            }

            return QueryResult.Create(results);
        }
    }
}
