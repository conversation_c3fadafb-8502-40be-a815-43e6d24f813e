using System.Data;
using System.Data.Common;
using TinyCRM.DynamicForm.Command;
using Webaby;
using Webaby.Data;
using Microsoft.EntityFrameworkCore;

namespace TinyCRM.RequestTicket.Commands
{
    public class ReCalculateDynamicFormValueListCommand : CommandBase
    {
        public List<Guid> RequestTicketList { get; set; }
    }

    internal class ReCalculateDynamicFormValueListCommandHandler : CommandHandlerBase<ReCalculateDynamicFormValueListCommand>
    {
        public ReCalculateDynamicFormValueListCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(ReCalculateDynamicFormValueListCommand command)
        {
            // ✅ <PERSON>y<PERSON>n đổi từ stored procedure sang LINQ query
            // Thay thế stored procedure "dbo.GetListOfTicketDynamicFormValueId"
            var ticketDynamicFormValueIds = await (from rt in EntitySet.Get<RequestTicketEntity>()
                                                   where command.RequestTicketList.Contains(rt.Id)
                                                   select new TicketDynamicFormValueId
                                                   {
                                                       RequestTicketId = rt.Id,
                                                       DynamicFormValueId = rt.DynamicFormValueId
                                                   }).ToListAsync();

            foreach (var ticketDynamicFormValueId in ticketDynamicFormValueIds)
            {
                if (ticketDynamicFormValueId.DynamicFormValueId.HasValue)
                {
                    await CommandExecutor.ExecuteAsync(new ReCalculateDynamicFormValueCommand { DynamicFormValueId = ticketDynamicFormValueId.DynamicFormValueId.Value });
                }
            }
        }

        private class TicketDynamicFormValueId
        {
            public Guid RequestTicketId { get; set; }
            public Guid? DynamicFormValueId { get; set; }
        }
    }
}