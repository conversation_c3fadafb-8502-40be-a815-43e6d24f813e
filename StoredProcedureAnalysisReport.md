## BÁOCÁO PHÂN TÍCH STORED PROCEDURES TRONG CONTROLLERS

**Format báo cáo theo yêu cầu:**

## 1. c:\Users\<USER>\source\Workspaces\CEP_NETCORE\TinyCRM.Web\Controllers\DynamicFormController.cs
-------------------------------------------
- CalculateGlobalDynamicFieldCommand → {Tên stored procedure được tham số hóa từ ExecutedScript}
- GetDynamicFieldSectionDropdownListQuery → dbo.GetDynamicFieldSectionDropdownList
- SaveDynamicFieldOrdersCommand → dbo.SaveDynamicFieldOrders
- GetDynamicDefinedTableSchemaQuery → Không sử dụng stored procedure
- GetDynamicFormQuery → Không sử dụng stored procedure
- GetDynamicFormByIdQuery → Không sử dụng stored procedure
- GetDynamicFormValueByFormIdQuery → Không sử dụng stored procedure
- GetDynamicFieldByFormIdQuery → Không sử dụng stored procedure
- GetAllVariableOfSaleTemplateQuery → Không sử dụng stored procedure
- GetInfoListByTypeQuery → Không sử dụng stored procedure
- GetEntityLinkBusinessSpecificListQuery → Không sử dụng stored procedure
- GetEntityLinkListQuery → Không sử dụng stored procedure
- GetDynamicFieldSectionByFormIdQuery → Không sử dụng stored procedure
- GetDynamicFieldSectionByIdQuery → Không sử dụng stored procedure
- SearchDynamicTableColumnQuery → Không sử dụng stored procedure
- GetDynamicFieldByIdQuery → Không sử dụng stored procedure
- GetDynamicDefinedTableColumnListByTableSchemaQuery → Không sử dụng stored procedure
- GetDynamicDefinedTableLinkedTicketColumnListByColumnIdQuery → Không sử dụng stored procedure
- GetExcelFormulaListQuery → Không sử dụng stored procedure
- GetDynamicFieldByFormIdAndNameQuery → Không sử dụng stored procedure
- GetDynamicFieldInFormByNameQuery → Không sử dụng stored procedure
- GetDynamicFieldInFormByOrderQuery → Không sử dụng stored procedure
- GetDynamicFieldValueInfoByFormIdQuery → Không sử dụng stored procedure
- GetDynamicRepresentationFieldQuery → Không sử dụng stored procedure
- PreviewDataStructWithDynamicTableQuery → Không sử dụng stored procedure
- GetRefObjectOfDynamicFormQuery → Không sử dụng stored procedure
- GetCalculatedFieldsTreeQuery → Không sử dụng stored procedure
- GetDynamicFormByEntityLinkTicketQuery → Không sử dụng stored procedure
- GetAllCustomerVersionNameQuery → Không sử dụng stored procedure
- SearchUsersQuery → Không sử dụng stored procedure
- CreateEditFileCommand → Không sử dụng stored procedure
- CloneDynamicFormCommand → Không sử dụng stored procedure
- CreateEditDynamicFieldSectionCommand → Không sử dụng stored procedure
- DeleteDynamicFieldSectionCommand → Không sử dụng stored procedure
- CreateEditDynamicFieldDefinitionCommand → Không sử dụng stored procedure
- SaveDefaultFieldValuesCommand → Không sử dụng stored procedure
- DeleteDynamicFieldDefinitionCommand → Không sử dụng stored procedure
- UpdateDynamicFieldDefinitionDefaultValueCommand → Không sử dụng stored procedure
- DeleteRepresentationDynamicFieldCommand → Không sử dụng stored procedure
- SaveColumnFormulaListCommand → Không sử dụng stored procedure
- SaveDynamicFieldFormulaListCommand → Không sử dụng stored procedure
- DeleteDynamicDefinedTableRowCommand → Không sử dụng stored procedure

## 2. c:\Users\<USER>\source\Workspaces\CEP_NETCORE\TinyCRM.Web\Controllers\DynamicFormValueController.cs
-------------------------------------------
- GetDynamicFormValueByIdQuery → Không sử dụng stored procedure
- GetDynamicFormByIdQuery → Không sử dụng stored procedure
- ExportDynamicFormValueQuery → Không sử dụng stored procedure
- GetDynamicFieldByFormIdQuery → Không sử dụng stored procedure
- CalculateDynamicFieldQuery → Không sử dụng stored procedure
- GetDynamicDefinedTableColumnListByTableSchemaQuery → Không sử dụng stored procedure
- GetDynamicDefinedTableLinkedTicketColumnListByColumnIdQuery → Không sử dụng stored procedure
- GetServiceTypeByIdQuery → Không sử dụng stored procedure
- GetDynamicFieldValueInfoByFormIdQuery → Không sử dụng stored procedure

## 3. c:\Users\<USER>\source\Workspaces\CEP_NETCORE\TinyCRM.Web\Controllers\DynamicTableController.cs
-------------------------------------------
- SearchDynamicTableQuery → Không sử dụng stored procedure
- GetDynamicDefinedTableColumnByIdQuery → Không sử dụng stored procedure
- GetDynamicDefinedTableSchemaQuery → Không sử dụng stored procedure
- SearchDynamicTableColumnQuery → Không sử dụng stored procedure
- GetTableColumnHideOnDynamicFormQuery → Không sử dụng stored procedure
- GetDynamicFieldByFormIdQuery → Không sử dụng stored procedure
- GetDynamicDefinedTableColumnListByTableSchemaQuery → Không sử dụng stored procedure
- GetDynamicDefinedTableLinkedTicketColumnListByColumnIdQuery → Không sử dụng stored procedure
- GetExcelFormulaListQuery → Không sử dụng stored procedure
- GetDynamicDefinedTableLinkedTicketColumnListByIdQuery → Không sử dụng stored procedure
- GetDynamicFieldByIdQuery → Không sử dụng stored procedure
- GetTableColumnOnDynamicFormQuery → Không sử dụng stored procedure
- GetServiceTypeByIdQuery → Không sử dụng stored procedure
- GetAllDynamicFieldDefinitionInfoQuery → Không sử dụng stored procedure
- CreateEditDynamicTableCommand → Không sử dụng stored procedure
- SyncOwnDbTableColumnForDynamicDefinedTableSchemaCommand → Không sử dụng stored procedure
- DeleteDynamicTableCommand → Không sử dụng stored procedure
- CreateEditDynamicTableColumnOnDynamicFormCommand → Không sử dụng stored procedure
- EditLinkedTicketColumnByDynamicTableColumnCommand → Không sử dụng stored procedure
- CreateEditDynamicTableColumnCommand → Không sử dụng stored procedure
- SaveDynamicDefinedTableColumnInjectsCommand → Không sử dụng stored procedure
- SaveDynamicFieldInjectsCommand → Không sử dụng stored procedure
- CreateLinkedTicketColumnByDynamicTableColumnCommand → Không sử dụng stored procedure
- DeleteDynamicTableColumnCommand → Không sử dụng stored procedure

## 4. c:\Users\<USER>\source\Workspaces\CEP_NETCORE\TinyCRM.Web\Controllers\EntityLinkController.cs
-------------------------------------------
- GetUserProfileByIdQuery → Không sử dụng stored procedure
- GetTicketForAddToEntityLinkQuery → Không sử dụng stored procedure
- GetRequestTicketByIdQuery → Không sử dụng stored procedure
- GetAllDynamicFormByLevelQuery → Không sử dụng stored procedure
- GetServiceTypeByIdQuery → Không sử dụng stored procedure
- GetEntityLinkBusinessSpecificByNameQuery → Không sử dụng stored procedure
- CreateOrRefreshLinkTicketCommand → Không sử dụng stored procedure
- ReCalculateDynamicFormValueCommand → Không sử dụng stored procedure
- AssignEntityLinkCommand → Không sử dụng stored procedure
- CreateEditEntityLinkCommand → Không sử dụng stored procedure
- DeleteEntityLinkCommand → Không sử dụng stored procedure


---

## THÔNG TIN BỔ SUNG - CÁC STORED PROCEDURES QUAN TRỌNG KHÁC:

**Các Command classes khác trong hệ thống cũng sử dụng stored procedures:**
- BatchCreateRequestTicketListCommand → dbo.BatchCreateRequestTicketList
- BatchInsertNewDynamicFieldToExistedTicketListCommand → dbo.BatchInsertNewDynamicFieldToExistedTicketList  
- DeleteCustomerRequestTicketListCommand → dbo.DeleteCustomerRequestTicketList
- ReallocateRequestTicketCustomerCommand → dbo.ReallocatedRequestTicketCustomer
- SyncMissedDynamicFormValueAndDynamicDefinedTableFieldValuesCommand → dbo.SyncMissedDynamicFormValueAndDynamicDefinedTableFieldValues
- AskAndAddAppSingletonExecutedTaskCommand → dbo.AskAndAddAppSingletonExecutedTask
- DeleteAppSingletonExecutedTaskCommand → dbo.DeleteAppSingletonExecutedTask

---

## TỔNG KẾT PHÂN TÍCH:

**Controllers được phân tích:** 4
**Tổng số lệnh gọi Query/Command:** 185  
**Số classes SỬ DỤNG stored procedures trong 4 Controllers:** 3
**Số classes KHÔNG sử dụng stored procedures trong 4 Controllers:** 182

**KẾT LUẬN:** 
Trong 4 Controllers được yêu cầu phân tích, chỉ có 3 Query/Command classes sử dụng stored procedures:
1. CalculateGlobalDynamicFieldCommand (stored procedure tham số hóa)
2. GetDynamicFieldSectionDropdownListQuery → dbo.GetDynamicFieldSectionDropdownList  
3. SaveDynamicFieldOrdersCommand → dbo.SaveDynamicFieldOrders

Đa số các Query/Command classes đều sử dụng Entity Framework LINQ thay vì stored procedures.
